import { getPrismaClient } from '@/lib/prisma';
import ProductsList from '@/components/admin/ProductsList';

export default async function AdminProductsPage() {
  const prisma = getPrismaClient();

  // Fetch products with categories and images
  const products = await prisma.product.findMany({
    include: {
      category: true,
      images: {
        take: 1,
        orderBy: { sortOrder: 'asc' }
      },
      _count: {
        select: { variants: true }
      }
    },
    orderBy: { createdAt: 'desc' },
    take: 50
  });

  const totalProducts = await prisma.product.count();
  const activeProducts = await prisma.product.count({ where: { isActive: true } });
  const lowStockProducts = await prisma.product.count({ 
    where: { 
      trackQuantity: true,
      quantity: { lte: 10 }
    }
  });

  return (
    <ProductsList
      initialProducts={products}
      totalProducts={totalProducts}
      activeProducts={activeProducts}
      lowStockProducts={lowStockProducts}
    />
  );
}
