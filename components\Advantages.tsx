'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { Recycle, Lock, TrendingUp, DollarSign, ThumbsUp, Package, Sparkles, Star, Zap, ChevronRight } from 'lucide-react';

interface AdvantageCard {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  gradient: string;
}

export default function Advantages() {
  const [shuffledCards, setShuffledCards] = useState<AdvantageCard[]>([]);
  const [hasShuffled, setHasShuffled] = useState(false);
  const [activeCard, setActiveCard] = useState<number | null>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: false, amount: 0.3 });

  const advantageCards: AdvantageCard[] = useMemo(() => [
    {
      id: 1,
      title: "Réutilisable et refermable",
      description: "Les pochettes ZIPBAGS® sont réutilisables et refermables pour de multiples utilisations gardant le contenu frais et exempt d'odeurs, d'humidité et d'oxygène dans un environnement hermétique.",
      icon: <Recycle className="h-6 w-6" />,
      color: "from-emerald-400 to-emerald-600",
      gradient: "bg-gradient-to-br from-emerald-400/20 via-emerald-500/30 to-emerald-600/20"
    },
    {
      id: 2,
      title: "Thermoscellable",
      description: "Les sacs thermoscellés offrent une finition inviolable à laquelle les clients s'attendent. Le thermoscellage prolonge la durée de conservation du produit, maintient la fraîcheur et garantit la sécurité alimentaire.",
      icon: <Lock className="h-6 w-6" />,
      color: "from-blue-400 to-blue-600",
      gradient: "bg-gradient-to-br from-blue-400/20 via-blue-500/30 to-blue-600/20"
    },
    {
      id: 3,
      title: "Impact d'étagère",
      description: "Les sachets ZIPBAGS® sont réutilisables et refermables pour de multiples utilisations gardant le contenu frais et exempt d'odeurs, d'humidité et d'oxygène dans un environnement hermétique.",
      icon: <TrendingUp className="h-6 w-6" />,
      color: "from-purple-400 to-purple-600",
      gradient: "bg-gradient-to-br from-purple-400/20 via-purple-500/30 to-purple-600/20"
    },
    {
      id: 4,
      title: "Économies de coûts",
      description: "Les pochettes ZIPBAGS® offrent une solution d'emballage tout-en-un; aucun bouchon, couvercle ou insert requis. L'emballage flexible coûte généralement trois à six fois moins cher par unité qu'un emballage rigide",
      icon: <DollarSign className="h-6 w-6" />,
      color: "from-green-400 to-green-600",
      gradient: "bg-gradient-to-br from-green-400/20 via-green-500/30 to-green-600/20"
    },
    {
      id: 5,
      title: "Commodité client",
      description: "Les consommateurs apprécient la commodité. Nos sachets debout sont conçus avec des encoches de déchirure faciles à ouvrir, des fermetures à glissière hermétiques refermables et prennent moins de place sur les étagères que les bouteilles, canettes et pots rigides traditionnels.",
      icon: <ThumbsUp className="h-6 w-6" />,
      color: "from-amber-400 to-amber-600",
      gradient: "bg-gradient-to-br from-amber-400/20 via-amber-500/30 to-amber-600/20"
    },
    {
      id: 6,
      title: "Frais d'entreposage et d'expédition",
      description: "Nos pochettes ZIPBAGS® sont expédiés et stockés à plat, ce qui nécessite beaucoup moins d'espace d'entrepôt. De plus, la légèreté des sacs debout réduit considérablement les coûts d'expédition par rapport aux bouteilles, bocaux et autres contenants rigides.",
      icon: <Package className="h-6 w-6" />,
      color: "from-cyan-400 to-cyan-600",
      gradient: "bg-gradient-to-br from-cyan-400/20 via-cyan-500/30 to-cyan-600/20"
    }
  ], []);

  // Function to shuffle the cards
  const shuffleCards = useCallback(() => {
    const currentCards = [...advantageCards];
    for (let i = currentCards.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [currentCards[i], currentCards[j]] = [currentCards[j], currentCards[i]];
    }
    setShuffledCards(currentCards);
  }, [advantageCards]);

  // Initialize shuffled cards
  useEffect(() => {
    setShuffledCards([...advantageCards]);
  }, [advantageCards]);

  // Shuffle cards when section comes into view
  useEffect(() => {
    if (isInView && !hasShuffled) {
      const timer = setTimeout(() => {
        shuffleCards();
        setHasShuffled(true);
      }, 300);
      return () => clearTimeout(timer);
    } else if (!isInView) {
      setHasShuffled(false);
    }
  }, [isInView, hasShuffled, shuffleCards]);

  return (
    <section ref={sectionRef} className="relative py-16 overflow-hidden sm:mt-[-120px]">
      {/* Interactive Particle Background */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            initial={{
              x: Math.random() * 100,
              y: Math.random() * 100,
              opacity: 0.3,
              scale: 0.5 + Math.random()
            }}
            animate={{
              x: [null, (Math.random() - 0.5) * 50],
              y: [null, (Math.random() - 0.5) * 50],
              transition: {
                duration: 10 + Math.random() * 20,
                repeat: Infinity,
                repeatType: "reverse"
              }
            }}
            className={`absolute rounded-full ${i % 3 === 0 ? 'bg-emerald-400/20' : i % 2 === 0 ? 'bg-blue-400/20' : 'bg-green-400/20'}`}
            style={{
              width: `${5 + Math.random() * 10}px`,
              height: `${5 + Math.random() * 10}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              filter: 'blur(1px)'
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Animated Header */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16 relative"
        >
          <motion.div 
            whileHover={{ scale: 1.05 }}
            className="inline-flex items-center gap-3 bg-white/90 backdrop-blur-xl px-8 py-4 rounded-full shadow-lg border border-green-200/50 mb-6"
          >
            <Sparkles className="w-5 h-5 text-green-600 animate-pulse" />
            <span className="text-sm font-bold text-green-800 tracking-wider uppercase">
              Innovation • Excellence • Performance
            </span>
            <Star className="w-5 h-5 text-green-600 animate-pulse" style={{ animationDelay: '0.3s' }} />
          </motion.div>

          <motion.h2 
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : {}}
            transition={{ delay: 0.2 }}
            className="text-4xl md:text-5xl lg:text-6xl font-black mb-6 relative"
          >
            <span className="block text-gray-900 mb-2">Les Avantages</span>
            <span className="block bg-gradient-to-r from-green-600 via-emerald-500 to-green-600 bg-clip-text text-transparent">
              ZIPBAGS®
            </span>
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-green-500 to-transparent rounded-full" />
          </motion.h2>

          <motion.p 
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : {}}
            transition={{ delay: 0.4 }}
            className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed font-light"
          >
            Découvrez pourquoi nos solutions d'emballage révolutionnent l'industrie avec des avantages inégalés
          </motion.p>
        </motion.div>

        {/* Interactive Card Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          <AnimatePresence>
            {shuffledCards.map((card) => (
              <motion.div
                key={card.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, type: "spring" }}
                className="relative"
              >
                {/* 3D Card Effect */}
                <motion.div
                  className={`relative h-full rounded-3xl overflow-hidden ${card.gradient}`}
                  whileHover={{ 
                    y: -10,
                    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                  }}
                  onHoverStart={() => setActiveCard(card.id)}
                  onHoverEnd={() => setActiveCard(null)}
                >
                  {/* Card Content */}
                  <div className="relative z-10 bg-white/90 backdrop-blur-sm h-full p-8 flex flex-col">
                    {/* Icon with Floating Effect */}
                    <motion.div 
                      animate={activeCard === card.id ? { y: -5 } : { y: 0 }}
                      className="relative mb-6 w-16 h-16 flex items-center justify-center"
                    >
                      <div className={`absolute inset-0 rounded-2xl ${card.gradient} opacity-60 blur-md`} />
                      <div className={`relative ${card.color} p-4 rounded-2xl text-white shadow-lg`}>
                        {card.icon}
                      </div>
                    </motion.div>

                    {/* Title with Gradient Text */}
                    <h3 className={`text-2xl font-bold mb-4 bg-gradient-to-r ${card.color} bg-clip-text text-transparent`}>
                      {card.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 leading-relaxed flex-grow font-light mb-6">
                      {card.description}
                    </p>

                    {/* Interactive Button */}
                    <motion.div 
                      whileHover={{ x: 5 }}
                      className="flex items-center text-sm font-medium self-end"
                    >
                      <span className={`bg-gradient-to-r ${card.color} bg-clip-text text-transparent`}>
                        En savoir plus
                      </span>
                      <ChevronRight className={`w-4 h-4 ml-1 ${card.color.replace('from-', 'text-').replace(' to-', '')}`} />
                    </motion.div>

                    {/* Hover Effect Elements */}
                    {activeCard === card.id && (
                      <>
                        <motion.div 
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className={`absolute -bottom-4 -right-4 w-32 h-32 ${card.gradient} rounded-full blur-xl`}
                        />
                        <motion.div 
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 0.3 }}
                          className={`absolute -top-4 -left-4 w-24 h-24 ${card.gradient} rounded-full blur-xl`}
                        />
                      </>
                    )}
                  </div>

                  {/* Decorative Border */}
                  <div className={`absolute inset-0 rounded-3xl ${card.gradient} p-[2px] pointer-events-none`}>
                    <div className="w-full h-full bg-white/90 rounded-[calc(1.5rem-2px)]" />
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Floating CTA */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ delay: 0.6 }}
          className="mt-16 text-center"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-full shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
          >
            <span className="relative z-10">Découvrir toute la gamme</span>
            <motion.div 
              initial={{ x: '-100%' }}
              whileHover={{ x: '100%' }}
              transition={{ duration: 1 }}
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            />
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}