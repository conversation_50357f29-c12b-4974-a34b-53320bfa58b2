import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateAdminRole() {
  try {
    console.log('Updating SUPER_ADMIN users to ADMIN...');
    
    // Update all SUPER_ADMIN users to ADMIN
    const result = await prisma.$executeRaw`
      UPDATE "User" SET role = 'ADMIN' WHERE role = 'SUPER_ADMIN';
    `;
    
    console.log(`Updated ${result} users from SUPER_ADMIN to ADMIN`);
    
    // Verify the update
    const adminUsers = await prisma.user.findMany({
      where: { role: 'ADMIN' },
      select: { email: true, role: true }
    });
    
    console.log('Current admin users:', adminUsers);
    
  } catch (error) {
    console.error('Error updating admin roles:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateAdminRole();
871563