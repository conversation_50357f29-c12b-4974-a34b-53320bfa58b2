// Custom Product Types for PackedIn E-commerce

export interface ProductImage {
  id: string;
  url: string;
  altText?: string;
  sortOrder: number;
}

export interface ProductVariant {
  id: string;
  name: string;
  sku?: string;
  price?: number;
  quantity: number;
  isActive: boolean;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  isActive: boolean;
  sortOrder: number;
  _count?: {
    products: number;
  };
}

export interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  sku?: string;
  price: number;
  comparePrice?: number;
  costPrice?: number;
  trackQuantity: boolean;
  quantity: number;
  lowStockThreshold: number;
  weight?: number;
  dimensions?: string;
  metaTitle?: string;
  metaDescription?: string;
  isActive: boolean;
  isFeatured: boolean;
  categoryId?: string;
  category?: Category;
  images: ProductImage[];
  variants: ProductVariant[];
  createdAt: Date;
  updatedAt: Date;
}

// For compatibility with existing components that expect Shopify format
export interface ShopifyProduct {
  id: string;
  title: string;
  handle: string;
  description: string;
  descriptionHtml: string;
  priceRange: {
    minVariantPrice: {
      amount: string;
      currencyCode: string;
    };
    maxVariantPrice: {
      amount: string;
      currencyCode: string;
    };
  };
  compareAtPriceRange?: {
    minVariantPrice: {
      amount: string;
      currencyCode: string;
    };
    maxVariantPrice: {
      amount: string;
      currencyCode: string;
    };
  };
  images: {
    edges: Array<{
      node: ShopifyImage;
    }>;
  };
  variants: {
    edges: Array<{
      node: {
        id: string;
        title: string;
        price: {
          amount: string;
          currencyCode: string;
        };
        compareAtPrice?: {
          amount: string;
          currencyCode: string;
        };
        availableForSale: boolean;
        quantityAvailable?: number;
      };
    }>;
  };
  tags: string[];
  vendor: string;
  productType: string;
  createdAt: string;
  updatedAt: string;
}

export interface ShopifyImage {
  id: string;
  url: string;
  altText?: string;
  width: number;
  height: number;
}

// Helper function to convert our Product to Shopify format for compatibility
export function convertToShopifyFormat(product: Product): ShopifyProduct {
  return {
    id: product.id,
    title: product.name,
    handle: product.slug,
    description: product.description || '',
    descriptionHtml: product.description || '',
    priceRange: {
      minVariantPrice: {
        amount: product.price.toString(),
        currencyCode: 'TND',
      },
      maxVariantPrice: {
        amount: product.price.toString(),
        currencyCode: 'TND',
      },
    },
    compareAtPriceRange: product.comparePrice ? {
      minVariantPrice: {
        amount: product.comparePrice.toString(),
        currencyCode: 'TND',
      },
      maxVariantPrice: {
        amount: product.comparePrice.toString(),
        currencyCode: 'TND',
      },
    } : undefined,
    images: {
      edges: product.images.map(image => ({
        node: {
          id: image.id,
          url: image.url,
          altText: image.altText,
          width: 800,
          height: 800,
        },
      })),
    },
    variants: {
      edges: product.variants.map(variant => ({
        node: {
          id: variant.id,
          title: variant.name,
          price: {
            amount: (variant.price || product.price).toString(),
            currencyCode: 'TND',
          },
          availableForSale: variant.isActive && variant.quantity > 0,
          quantityAvailable: variant.quantity,
        },
      })),
    },
    tags: [],
    vendor: 'PackedIn',
    productType: product.category?.name || 'General',
    createdAt: product.createdAt.toISOString(),
    updatedAt: product.updatedAt.toISOString(),
  };
}

// Collection/Category types for compatibility
export interface Collection {
  id: string;
  title: string;
  handle: string;
  description?: string;
  image?: {
    url: string;
    altText?: string;
  };
  products: {
    edges: Array<{
      node: ShopifyProduct;
    }>;
  };
}

export function convertCategoryToCollection(category: Category, products: Product[]): Collection {
  return {
    id: category.id,
    title: category.name,
    handle: category.slug,
    description: category.description,
    image: category.image ? {
      url: category.image,
      altText: category.name,
    } : undefined,
    products: {
      edges: products.map(product => ({
        node: convertToShopifyFormat(product),
      })),
    },
  };
}
