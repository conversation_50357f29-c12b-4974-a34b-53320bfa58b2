import { getPrismaClient } from './lib/prisma.ts';

async function testDatabase() {
  try {
    console.log('Testing database connection...');
    
    const prisma = getPrismaClient();
    
    // Test basic connection
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection successful:', result);
    
    // Test user table
    const userCount = await prisma.user.count();
    console.log('✅ User table accessible, count:', userCount);
    
    // Test if we can find users
    const users = await prisma.user.findMany({
      take: 1,
      select: {
        id: true,
        email: true,
        name: true
      }
    });
    console.log('✅ Sample user data:', users);
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
}

testDatabase();
