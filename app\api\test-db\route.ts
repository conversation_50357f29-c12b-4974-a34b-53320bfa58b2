import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('Testing database connection...');

    // Check if database URL is available
    if (!process.env.DATABASE_URL) {
      console.log('❌ DATABASE_URL not configured');
      return NextResponse.json({
        success: false,
        error: 'DATABASE_URL not configured'
      }, { status: 503 });
    }

    console.log('✅ DATABASE_URL found:', process.env.DATABASE_URL.substring(0, 20) + '...');

    // Try to import Prisma client
    let prisma;
    try {
      const { getPrismaClient } = await import('@/lib/prisma');
      prisma = getPrismaClient();
      console.log('✅ Prisma client imported successfully');
    } catch (importError) {
      console.error('❌ Failed to import Prisma client:', importError);
      return NextResponse.json({
        success: false,
        error: `Failed to import Prisma client: ${importError instanceof Error ? importError.message : 'Unknown error'}`
      }, { status: 500 });
    }
    
    // Test basic connection
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection successful:', result);
    
    // Test user table
    const userCount = await prisma.user.count();
    console.log('✅ User table accessible, count:', userCount);
    
    // Test if we can find users
    const users = await prisma.user.findMany({
      take: 1,
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true
      }
    });
    console.log('✅ Sample user data:', users);
    
    return NextResponse.json({
      success: true,
      data: {
        connectionTest: result,
        userCount,
        sampleUsers: users
      }
    });
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
