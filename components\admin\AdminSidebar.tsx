'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard, 
  Package, 
  FolderOpen, 
  ShoppingCart, 
  Users, 
  FileText,
  Settings,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AdminSidebarProps {
  userRole: string;
}

const menuItems = [
  {
    title: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
    roles: ['ADMIN', 'SUPER_ADMIN'],
  },
  {
    title: 'Products',
    href: '/admin/products',
    icon: Package,
    roles: ['ADMIN', 'SUPER_ADMIN'],
  },
  {
    title: 'Categories',
    href: '/admin/categories',
    icon: FolderOpen,
    roles: ['ADMIN', 'SUPER_ADMIN'],
  },
  {
    title: 'Orders',
    href: '/admin/orders',
    icon: ShoppingCart,
    roles: ['ADMIN', 'SUPER_ADMIN'],
  },
  {
    title: 'Customers',
    href: '/admin/customers',
    icon: Users,
    roles: ['ADMIN', 'SUPER_ADMIN'],
  },
  {
    title: 'Quotes',
    href: '/admin/quotes',
    icon: FileText,
    roles: ['ADMIN', 'SUPER_ADMIN'],
  },
  {
    title: 'Analytics',
    href: '/admin/analytics',
    icon: BarChart3,
    roles: ['SUPER_ADMIN'],
  },
  {
    title: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    roles: ['SUPER_ADMIN'],
  },
];

export default function AdminSidebar({ userRole }: AdminSidebarProps) {
  const pathname = usePathname();

  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(userRole)
  );

  return (
    <div className="fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 bg-white border-r border-gray-200 overflow-y-auto">
      <div className="p-4">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Administration</h2>
        <nav className="space-y-2">
          {filteredMenuItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                  isActive
                    ? 'bg-green-100 text-green-700'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                )}
              >
                <Icon className="h-5 w-5" />
                {item.title}
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );
}
