@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced Professional Color Palette */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* Sophisticated Primary Colors */
    --primary: 222 47% 11%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    /* Refined Neutral Tones */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 94%;
    --accent-foreground: 222 47% 11%;

    /* Professional Accent Colors */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Enhanced Borders & Inputs */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 4.9%;

    /* Premium Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(222 47% 11%) 0%, hsl(215 25% 27%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(210 40% 96%) 0%, hsl(214 32% 91%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(158 64% 52%) 100%);

    /* Enhanced Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Professional Radius System */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* Chart Colors */
    --chart-1: 222 47% 11%;
    --chart-2: 142 76% 36%;
    --chart-3: 38 92% 50%;
    --chart-4: 271 81% 56%;
    --chart-5: 0 84% 60%;
  }
  .dark {
    /* Enhanced Dark Mode Palette */
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Sophisticated Dark Primary Colors */
    --primary: 210 40% 98%;
    --primary-foreground: 222 47% 11%;
    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;

    /* Refined Dark Neutral Tones */
    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 217 32% 17%;
    --accent-foreground: 210 40% 98%;

    /* Professional Dark Accent Colors */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 98%;

    /* Enhanced Dark Borders & Inputs */
    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 212 72% 59%;

    /* Premium Dark Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(210 40% 98%) 0%, hsl(212 72% 59%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(217 32% 17%) 0%, hsl(222 84% 4.9%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(158 64% 52%) 100%);

    /* Chart Colors for Dark Mode */
    --chart-1: 210 40% 98%;
    --chart-2: 142 76% 36%;
    --chart-3: 38 92% 50%;
    --chart-4: 271 81% 56%;
    --chart-5: 0 84% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Enhanced Typography - Mobile First */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
    line-height: 1.1;
  }

  h2 {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
    line-height: 1.2;
  }

  h3 {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
    line-height: 1.3;
  }

  h4 {
    @apply text-base sm:text-lg md:text-xl lg:text-2xl;
    line-height: 1.4;
  }

  /* Professional Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Mobile-First Responsive Utilities */
  .text-responsive {
    @apply text-sm sm:text-base md:text-lg;
  }

  .text-responsive-lg {
    @apply text-base sm:text-lg md:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl;
  }

  .padding-responsive {
    @apply p-3 sm:p-4 md:p-6 lg:p-8;
  }

  .margin-responsive {
    @apply m-3 sm:m-4 md:m-6 lg:m-8;
  }

  .gap-responsive {
    @apply gap-3 sm:gap-4 md:gap-6 lg:gap-8;
  }

  /* Line Clamp Utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Touch-friendly interactive elements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Safe area for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Professional Animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Professional Glass Effect */
  .glass-effect {
    @apply bg-white/80 backdrop-blur-xl border border-white/20 shadow-xl;
  }

  .glass-effect-dark {
    @apply bg-gray-900/80 backdrop-blur-xl border border-gray-700/20 shadow-xl;
  }

  /* Premium Gradient Backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(222 47% 11%) 0%, hsl(215 25% 27%) 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, hsl(210 40% 96%) 0%, hsl(214 32% 91%) 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(158 64% 52%) 100%);
  }

  .gradient-warm {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  }

  .gradient-cool {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }

  /* Professional Hover Effects */
  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    @apply transform -translate-y-2 shadow-xl;
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out;
  }

  .hover-glow:hover {
    @apply shadow-lg shadow-primary/25;
  }

  /* Professional Focus States */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Professional Text Effects */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent;
  }

  .text-shimmer {
    background: linear-gradient(90deg, #000 25%, #666 50%, #000 75%);
    background-size: 200% 100%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Selection Styling */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
}

@layer utilities {
  /* Animations for navbar */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slideDown {
    animation: slideDown 0.3s ease-in-out;
  }

  /* Hero carousel animations */
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(5%);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-5%);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideOutLeft {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(-5%);
    }
  }

  @keyframes slideOutRight {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(5%);
    }
  }

  .animate-slide-in-right {
    animation: slideInRight 0.7s ease-in-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.7s ease-in-out forwards;
  }

  .animate-slide-out-left {
    animation: slideOutLeft 0.7s ease-in-out forwards;
  }

  .animate-slide-out-right {
    animation: slideOutRight 0.7s ease-in-out forwards;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.7s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Footer background animation */
  @keyframes gradient-shift {
    0% {
      background-position: 0% 0%;
    }
    100% {
      background-position: 100% 100%;
    }
  }

  .animate-gradient-shift {
    animation: gradient-shift 8s ease infinite alternate;
  }

  /* Enhanced Gradient Utilities */
  .bg-gradient-radial {
    background-image: radial-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-accent {
    background: var(--gradient-accent);
  }

  /* Professional Shadow Utilities */
  .shadow-soft {
    box-shadow: var(--shadow-sm);
  }

  .shadow-elegant {
    box-shadow: var(--shadow);
  }

  .shadow-professional {
    box-shadow: var(--shadow-md);
  }

  .shadow-premium {
    box-shadow: var(--shadow-lg);
  }

  .shadow-luxury {
    box-shadow: var(--shadow-xl);
  }

  /* Glass Morphism Effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced Hover Effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }

  .hover-scale {
    transition: transform 0.3s ease;
  }

  .hover-scale:hover {
    transform: scale(1.05);
  }

  /* Professional Button Styles */
  .btn-premium {
    @apply relative overflow-hidden bg-gradient-primary text-primary-foreground;
    @apply px-8 py-3 rounded-lg font-medium;
    @apply transition-all duration-300 ease-in-out;
    @apply hover:shadow-premium hover:-translate-y-1;
  }

  .btn-premium::before {
    content: '';
    @apply absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full;
    @apply transition-transform duration-700 ease-in-out;
  }

  .btn-premium:hover::before {
    @apply translate-x-full;
  }

  /* Card Enhancement */
  .card-premium {
    @apply bg-card border border-border rounded-xl shadow-elegant;
    @apply transition-all duration-300 ease-in-out;
    @apply hover:shadow-premium hover:-translate-y-1;
  }

  /* Text Gradients */
  .text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-accent {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced Hero Carousel Effects */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .shadow-4xl {
    box-shadow: 0 50px 100px -20px rgba(0, 0, 0, 0.25), 0 30px 60px -30px rgba(0, 0, 0, 0.3);
  }

  /* Premium Button Glow Effects */
  .btn-glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3), 0 0 40px rgba(34, 197, 94, 0.1);
  }

  .btn-glow-green:hover {
    box-shadow: 0 0 30px rgba(34, 197, 94, 0.4), 0 0 60px rgba(34, 197, 94, 0.2);
  }

  /* Glass Morphism Enhanced */
  .glass-premium {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  /* Floating Animation for Hero Elements */
  .float-gentle {
    animation: floatGentle 4s ease-in-out infinite;
  }

  @keyframes floatGentle {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-8px) rotate(1deg);
    }
  }

  /* Enhanced Pulse Animation */
  .pulse-soft {
    animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulseSoft {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  /* Line Clamp Utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
