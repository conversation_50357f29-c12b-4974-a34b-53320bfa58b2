'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Calculator, Sparkles, ArrowRight } from 'lucide-react';

export default function CTASection() {
  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 via-white to-green-50/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center relative"
        >
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-[0.02]">
              <div className="h-full w-full" style={{
                backgroundImage: `
                  linear-gradient(90deg, #10b981 1px, transparent 1px),
                  linear-gradient(180deg, #10b981 1px, transparent 1px)
                `,
                backgroundSize: '20px 20px'
              }} />
            </div>

            {/* Floating Calculator Icon */}
            <div className="absolute top-6 right-6 hidden md:block">
              <motion.div
                initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
                whileInView={{ opacity: 1, scale: 1, rotate: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.5 }}
                whileHover={{
                  scale: 1.1,
                  rotate: 5,
                  transition: { duration: 0.3 }
                }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  // Future: Open calculator modal
                  console.log('Calculator clicked - future feature');
                }}
                className="relative cursor-pointer group"
              >
                {/* Glow Effect */}
                <div className="absolute inset-0 bg-green-400 rounded-2xl blur-xl opacity-20 animate-pulse"></div>
                
                {/* Calculator Container */}
                <div className="relative bg-gradient-to-br from-green-500 to-green-600 p-4 rounded-2xl shadow-lg">
                  <Calculator className="h-8 w-8 text-white" />
                  
                  {/* Sparkle Effects */}
                  <motion.div
                    animate={{ 
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute -top-1 -right-1"
                  >
                    <Sparkles className="h-4 w-4 text-yellow-300" />
                  </motion.div>
                  
                  <motion.div
                    animate={{ 
                      scale: [1, 1.3, 1],
                      opacity: [0.3, 0.8, 0.3]
                    }}
                    transition={{ 
                      duration: 2.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 0.5
                    }}
                    className="absolute -bottom-1 -left-1"
                  >
                    <Sparkles className="h-3 w-3 text-blue-300" />
                  </motion.div>
                </div>

                {/* Tooltip */}
                <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap shadow-lg">
                  <div className="relative">
                    Calculateur de prix
                    {/* Tooltip Arrow */}
                    <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Content */}
            <div className="relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="inline-flex items-center gap-2 bg-green-100 px-4 py-2 rounded-full mb-6">
                  <Sparkles className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-semibold text-green-700 tracking-wide">
                    SUPPORT EXPERT
                  </span>
                </div>
              </motion.div>

              <motion.h3 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="text-2xl md:text-3xl font-bold text-gray-900 mb-4"
              >
                Besoin d'aide pour choisir ?
              </motion.h3>
              
              <motion.p 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="text-gray-600 mb-8 max-w-2xl mx-auto text-lg leading-relaxed"
              >
                Notre équipe d'experts est là pour vous accompagner dans le choix 
                de la solution d'emballage parfaite pour vos produits.
              </motion.p>
              
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.7 }}
                className="flex flex-col sm:flex-row gap-4 justify-center"
              >
                <Button 
                  size="lg" 
                  className="bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  Demander un devis
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                </Button>
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="border-green-300 text-green-700 hover:bg-green-50 shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  Contacter un expert
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                </Button>
              </motion.div>

              {/* Additional Info */}
              <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="mt-6 text-sm text-gray-500"
              >
                <span className="inline-flex items-center gap-1">
                  <Calculator className="h-4 w-4" />
                  Devis gratuit • Réponse sous 24h • Conseils personnalisés
                </span>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
