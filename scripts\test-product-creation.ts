import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testProductCreation() {
  try {
    console.log('🧪 Testing product creation...');
    
    // Get a category to use
    const category = await prisma.category.findFirst();
    console.log(`📁 Using category: ${category?.name || 'None'}`);
    
    // Create a test product
    const testProduct = await prisma.product.create({
      data: {
        name: 'Test Product - Auto Created',
        description: 'This is a test product created automatically',
        shortDescription: 'Test product for validation',
        price: 29.99,
        comparePrice: 39.99,
        sku: 'TEST-001',
        quantity: 100,
        lowStockThreshold: 10,
        weight: 0.5,
        isActive: true,
        isFeatured: false,
        trackQuantity: true,
        categoryId: category?.id || null,
        slug: 'test-product-auto-created',
        images: {
          create: [
            {
              url: 'https://via.placeholder.com/400x400/4ade80/ffffff?text=Test+Product',
              altText: 'Test Product - Main Image',
              sortOrder: 0,
            },
            {
              url: 'https://via.placeholder.com/400x400/3b82f6/ffffff?text=Test+Product+2',
              altText: 'Test Product - Secondary Image',
              sortOrder: 1,
            }
          ]
        }
      },
      include: {
        category: true,
        images: true,
      }
    });
    
    console.log('✅ Test product created successfully!');
    console.log(`   ID: ${testProduct.id}`);
    console.log(`   Name: ${testProduct.name}`);
    console.log(`   Price: ${testProduct.price} TND`);
    console.log(`   Category: ${testProduct.category?.name || 'None'}`);
    console.log(`   Images: ${testProduct.images.length} uploaded`);
    console.log(`   Slug: ${testProduct.slug}`);
    
    // Test fetching products
    const allProducts = await prisma.product.findMany({
      include: {
        category: true,
        images: true,
        _count: {
          select: { variants: true }
        }
      }
    });
    
    console.log(`\n📦 Total products in database: ${allProducts.length}`);
    
    // Clean up test product
    await prisma.product.delete({
      where: { id: testProduct.id }
    });
    
    console.log('🧹 Test product cleaned up');
    console.log('\n🎉 Product creation system is working correctly!');
    
  } catch (error) {
    console.error('❌ Error testing product creation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testProductCreation();
