# 📸 UploadThing Setup Guide

## 🚀 Quick Setup

### 1. Create UploadThing Account
1. Go to [uploadthing.com](https://uploadthing.com)
2. Sign up with GitHub or email
3. Create a new app
4. Copy your API keys

### 2. Update Environment Variables
Add these to your `.env` file:

```env
# UploadThing Configuration
UPLOADTHING_SECRET="sk_live_your-secret-key-here"
UPLOADTHING_APP_ID="your-app-id-here"
```

### 3. Features Included

#### 🖼️ **Product Images**
- **Endpoint**: `productImageUploader`
- **Max Files**: 10 images per product
- **Max Size**: 4MB per image
- **Formats**: JPEG, PNG, WebP
- **Access**: Admin only

#### 📁 **Category Images**
- **Endpoint**: `categoryImageUploader`
- **Max Files**: 1 image per category
- **Max Size**: 4MB
- **Formats**: JPEG, PNG, WebP
- **Access**: Admin only

#### 👤 **Profile Images**
- **Endpoint**: `profileImageUploader`
- **Max Files**: 1 image per user
- **Max Size**: 2MB
- **Formats**: JPEG, PNG, WebP
- **Access**: Any authenticated user

### 4. Usage in Admin

#### Product Creation
```tsx
// In admin product form
<ImageUpload
  value={formData.images}
  onChange={(urls) => setFormData(prev => ({ ...prev, images: urls }))}
  endpoint="productImageUploader"
  maxFiles={10}
/>
```

#### Category Creation
```tsx
// In admin category form
<ImageUpload
  value={[formData.image]}
  onChange={(urls) => setFormData(prev => ({ ...prev, image: urls[0] }))}
  endpoint="categoryImageUploader"
  maxFiles={1}
/>
```

### 5. Security Features

- ✅ **Authentication Required**: All uploads require valid session
- ✅ **Role-Based Access**: Admin endpoints restricted to ADMIN role
- ✅ **File Type Validation**: Only images allowed
- ✅ **Size Limits**: Configurable per endpoint
- ✅ **Auto-cleanup**: Failed uploads are automatically cleaned

### 6. File Organization

UploadThing automatically:
- Generates unique file names
- Provides CDN URLs
- Handles image optimization
- Manages file storage

### 7. Integration Points

#### Database Storage
Images URLs are stored in:
- `Product.images[]` - Array of image URLs
- `Category.image` - Single image URL
- `User.image` - Profile image URL

#### API Routes
- `/api/uploadthing` - Main upload handler
- Automatic webhook handling
- Error management

### 8. Development vs Production

#### Development
- Use test keys from UploadThing dashboard
- Files uploaded to development environment
- No cost for testing

#### Production
- Use production keys
- Configure custom domain (optional)
- Monitor usage in UploadThing dashboard

### 9. Troubleshooting

#### Common Issues
1. **"Unauthorized" Error**: Check user role and session
2. **File Too Large**: Verify size limits in configuration
3. **Invalid File Type**: Ensure only images are uploaded
4. **API Keys**: Verify environment variables are set correctly

#### Debug Mode
Enable debug logging:
```tsx
// In uploadthing config
export const ourFileRouter = {
  productImageUploader: f({ image: { maxFileSize: "4MB" } })
    .middleware(async ({ req }) => {
      console.log("Upload middleware called");
      // ... rest of middleware
    })
}
```

### 10. Cost Optimization

- **Free Tier**: 2GB storage, 10GB bandwidth
- **Paid Plans**: Start at $20/month
- **Optimization**: Use appropriate image sizes
- **Cleanup**: Remove unused images periodically

---

## 🎯 Ready to Use!

Your image upload system is now configured with:
- ✅ Secure file uploads
- ✅ Role-based access control
- ✅ Multiple upload endpoints
- ✅ Automatic file management
- ✅ CDN delivery

Just add your UploadThing API keys to `.env` and start uploading! 🚀
