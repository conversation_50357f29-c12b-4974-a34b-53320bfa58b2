# PackedIn E-commerce Platform

A modern, custom e-commerce platform built with Next.js, TypeScript, Tailwind CSS, and PostgreSQL. Features a complete admin dashboard with role-based access control.

## Features

- 🛍️ Custom e-commerce storefront
- 👨‍💼 Admin dashboard with role-based access
- 📦 Product and category management
- 🛒 Shopping cart functionality
- 📱 Responsive design with Tailwind CSS
- 🔍 Product search and filtering
- 🖼️ Image optimization with Next.js Image component
- 🔐 Secure authentication with NextAuth.js
- 📊 Analytics and reporting
- 💳 Order management system

## Prerequisites

- Node.js 18+ installed on your machine
- PostgreSQL database
- Environment variables configured

## Getting Started

1. Clone this repository:

```bash
git clone <repository-url>
cd packedin-ecommerce
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

Create a `.env` file in the root directory with the following variables:

```
# Application Configuration
APP_NAME="PackedIn E-commerce"
APP_URL="http://localhost:3000"
ADMIN_EMAIL="<EMAIL>"

# File Upload Configuration
UPLOAD_DIR="public/uploads"
MAX_FILE_SIZE="5242880"

# Auth.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET="your-secret-key"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/packedin"
```

4. Set up the database:

```bash
npx prisma migrate dev
npm run db:seed
```

5. Run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Project Structure

- `/app` - Next.js app router pages
- `/components` - Reusable React components
- `/context` - React context for state management
- `/lib` - Utility functions, hooks, and API clients
- `/public` - Static assets

## Customization

### Styling

This project uses Tailwind CSS for styling. You can customize the design by modifying the `tailwind.config.ts` file.

### Shopify Integration

The Shopify integration is handled through the Storefront API. You can modify the GraphQL queries in `lib/queries.ts` to fetch additional data or customize the existing queries.

## Deployment

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new) from the creators of Next.js.

Check out the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Learn More

To learn more about the technologies used in this project:

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Shopify Storefront API Documentation](https://shopify.dev/docs/api/storefront)
- [Bun Documentation](https://bun.sh/docs)

## License

This project is licensed under the MIT License.
# Test commit
