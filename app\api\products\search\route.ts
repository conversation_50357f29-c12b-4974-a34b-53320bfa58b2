import { NextRequest, NextResponse } from 'next/server';
import { getPrismaClient } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query');

    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      );
    }

    const prisma = getPrismaClient();

    const products = await prisma.product.findMany({
      where: {
        isActive: true,
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { sku: { contains: query, mode: 'insensitive' } },
        ],
      },
      include: {
        category: true,
        images: {
          orderBy: { sortOrder: 'asc' },
          take: 1,
        },
      },
      take: 20,
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({
      products: {
        edges: products.map(product => ({
          node: {
            id: product.id,
            title: product.name,
            handle: product.slug,
            description: product.description,
            priceRange: {
              minVariantPrice: {
                amount: product.price.toString(),
                currencyCode: 'TND',
              },
            },
            images: {
              edges: product.images.map(image => ({
                node: {
                  url: image.url,
                  altText: image.altText,
                },
              })),
            },
          },
        })),
      },
    });
  } catch (error) {
    console.error('Products Search API Error:', error);
    return NextResponse.json(
      { error: 'Error searching products' },
      { status: 500 }
    );
  }
}
