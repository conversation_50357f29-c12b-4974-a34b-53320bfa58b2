<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="pattern" width="60" height="60" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1.5" fill="white" />
      <circle cx="30" cy="10" r="1.5" fill="white" />
      <circle cx="50" cy="10" r="1.5" fill="white" />
      <circle cx="20" cy="20" r="1.5" fill="white" />
      <circle cx="40" cy="20" r="1.5" fill="white" />
      <circle cx="10" cy="30" r="1.5" fill="white" />
      <circle cx="30" cy="30" r="1.5" fill="white" />
      <circle cx="50" cy="30" r="1.5" fill="white" />
      <circle cx="20" cy="40" r="1.5" fill="white" />
      <circle cx="40" cy="40" r="1.5" fill="white" />
      <circle cx="10" cy="50" r="1.5" fill="white" />
      <circle cx="30" cy="50" r="1.5" fill="white" />
      <circle cx="50" cy="50" r="1.5" fill="white" />
    </pattern>
  </defs>
  <rect width="60" height="60" fill="url(#pattern)" />
</svg>
