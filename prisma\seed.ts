import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin PackedIn',
      password: hashedPassword,
      role: UserRole.SUPER_ADMIN,
      phone: '+216 50 095 115',
      address: 'Nabeul',
      city: 'Nabeul',
      country: 'TN',
      emailVerified: new Date(),
    },
  });

  console.log('✅ Admin user created:', adminUser.email);

  // Create categories based on your collections
  const categories = [
    {
      name: 'Pochettes Kraft',
      slug: 'kraftview-pochettes-zip-kraft-brun-avec-fenetre-transparente',
      description: 'Pochettes zip kraft brun avec fenêtre transparente',
      image: '/images/collections/kraftview-pochettes-zip-kraft-brun-avec-fenetre-transparente.jpg'
    },
    {
      name: 'Pochettes Transparentes',
      slug: 'fulltrans-pochettes-stand-up-transparentes-givrees',
      description: 'Pochettes stand-up transparentes givrées',
      image: '/images/collections/fulltrans™-pochettes-stand-up-transparentes-givrees.jpg'
    },
    {
      name: 'Pochettes Aluminium',
      slug: 'fullviewalu-pochettes-zip-aluminisees-avec-face-transparente',
      description: 'Pochettes zip aluminisées avec face transparente',
      image: '/images/collections/fullviewalu™-pochettes-zip-aluminisees-avec-face-transparente.jpg'
    },
    {
      name: 'Pochettes Aluminium Premium',
      slug: 'fullalu-pochettes-zip-en-aluminium-1',
      description: 'Pochettes zip en aluminium premium',
      image: '/images/collections/fullalu™-pochettes-zip-en-aluminium-1.jpg'
    }
  ];

  const createdCategories = [];
  for (const categoryData of categories) {
    const category = await prisma.category.upsert({
      where: { slug: categoryData.slug },
      update: {},
      create: categoryData,
    });
    createdCategories.push(category);
    console.log('✅ Category created:', category.name);
  }

  // Create products for each category
  const products = [
    // KraftView™ Products
    {
      name: 'Pochette Kraft 100x150mm',
      slug: 'pochette-kraft-100x150mm',
      description: 'Pochette zip kraft brun avec fenêtre transparente - Format 100x150mm',
      shortDescription: 'Pochette kraft avec fenêtre transparente',
      sku: 'KV-100x150',
      price: 0.25,
      comparePrice: 0.35,
      quantity: 1000,
      categoryId: createdCategories[0].id,
      isFeatured: true,
      weight: 5.2,
      dimensions: '{"length": 100, "width": 150, "height": 2}',
    },
    {
      name: 'Pochette Kraft 150x200mm',
      slug: 'pochette-kraft-150x200mm',
      description: 'Pochette zip kraft brun avec fenêtre transparente - Format 150x200mm',
      shortDescription: 'Pochette kraft avec fenêtre transparente',
      sku: 'KV-150x200',
      price: 0.35,
      comparePrice: 0.45,
      quantity: 800,
      categoryId: createdCategories[0].id,
      weight: 7.8,
      dimensions: '{"length": 150, "width": 200, "height": 2}',
    },
    // FullTrans™ Products
    {
      name: 'Pochette Transparente Stand-Up 100ml',
      slug: 'pochette-transparente-stand-up-100ml',
      description: 'Pochette stand-up transparente givrée - Capacité 100ml',
      shortDescription: 'Pochette transparente stand-up',
      sku: 'FT-100ML',
      price: 0.30,
      comparePrice: 0.40,
      quantity: 1200,
      categoryId: createdCategories[1].id,
      isFeatured: true,
      weight: 4.5,
      dimensions: '{"length": 80, "width": 120, "height": 40}',
    },
    {
      name: 'Pochette Transparente Stand-Up 250ml',
      slug: 'pochette-transparente-stand-up-250ml',
      description: 'Pochette stand-up transparente givrée - Capacité 250ml',
      shortDescription: 'Pochette transparente stand-up',
      sku: 'FT-250ML',
      price: 0.45,
      comparePrice: 0.55,
      quantity: 900,
      categoryId: createdCategories[1].id,
      weight: 6.2,
      dimensions: '{"length": 100, "width": 150, "height": 50}',
    },
    // FullViewAlu™ Products
    {
      name: 'Pochette Aluminium Transparente 100x150mm',
      slug: 'pochette-aluminium-transparente-100x150mm',
      description: 'Pochette zip aluminisée avec face transparente - Format 100x150mm',
      shortDescription: 'Pochette aluminium avec face transparente',
      sku: 'FVA-100x150',
      price: 0.40,
      comparePrice: 0.50,
      quantity: 600,
      categoryId: createdCategories[2].id,
      weight: 6.8,
      dimensions: '{"length": 100, "width": 150, "height": 3}',
    },
    // FullAlu™ Products
    {
      name: 'Pochette Aluminium Premium 150x200mm',
      slug: 'pochette-aluminium-premium-150x200mm',
      description: 'Pochette zip en aluminium premium - Format 150x200mm',
      shortDescription: 'Pochette aluminium premium',
      sku: 'FA-150x200',
      price: 0.55,
      comparePrice: 0.70,
      quantity: 500,
      categoryId: createdCategories[3].id,
      isFeatured: true,
      weight: 8.5,
      dimensions: '{"length": 150, "width": 200, "height": 4}',
    },
  ];

  for (const productData of products) {
    const product = await prisma.product.upsert({
      where: { slug: productData.slug },
      update: {},
      create: productData,
    });
    console.log('✅ Product created:', product.name);

    // Add sample images for each product
    await prisma.productImage.createMany({
      data: [
        {
          productId: product.id,
          url: `/images/products/${product.slug}-1.jpg`,
          altText: `${product.name} - Vue principale`,
          sortOrder: 0,
        },
        {
          productId: product.id,
          url: `/images/products/${product.slug}-2.jpg`,
          altText: `${product.name} - Vue détaillée`,
          sortOrder: 1,
        },
      ],
      skipDuplicates: true,
    });
  }

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
