'use client';

import { useState } from 'react';
import { UploadDropzone } from '@/lib/uploadthing-utils';
import { Button } from '@/components/ui/button';
import { X, Upload } from 'lucide-react';
import Image from 'next/image';
import { toast } from 'sonner';

interface ImageUploadProps {
  value: string[];
  onChange: (urls: string[]) => void;
  endpoint: 'productImageUploader' | 'categoryImageUploader' | 'profileImageUploader';
  maxFiles?: number;
}

export default function ImageUpload({ 
  value = [], 
  onChange, 
  endpoint,
  maxFiles = 10 
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);

  const onRemove = (url: string) => {
    onChange(value.filter((current) => current !== url));
  };

  return (
    <div className="space-y-4">
      {/* Display uploaded images */}
      {value.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {value.map((url, index) => (
            <div key={url} className="relative group">
              <div className="relative aspect-square rounded-lg overflow-hidden border border-gray-200">
                <Image
                  src={url}
                  alt={`Upload ${index + 1}`}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200" />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  onClick={() => onRemove(url)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              {index === 0 && (
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                  <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                    Main
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Upload dropzone */}
      {value.length < maxFiles && (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
          <UploadDropzone
            endpoint={endpoint}
            onClientUploadComplete={(res) => {
              if (res) {
                const newUrls = res.map((file) => file.url);
                onChange([...value, ...newUrls]);
                toast.success(`${res.length} image(s) uploaded successfully!`);
              }
              setIsUploading(false);
            }}
            onUploadError={(error: Error) => {
              toast.error(`Upload failed: ${error.message}`);
              setIsUploading(false);
            }}
            onUploadBegin={() => {
              setIsUploading(true);
            }}
            config={{
              mode: "auto",
            }}
            appearance={{
              container: "border-none p-0",
              uploadIcon: "text-gray-400",
              label: "text-gray-600 text-sm",
              allowedContent: "text-gray-500 text-xs",
              button: "bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",
            }}
          />
          
          {isUploading && (
            <div className="mt-4 text-center">
              <div className="inline-flex items-center gap-2 text-sm text-gray-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                Uploading images...
              </div>
            </div>
          )}
        </div>
      )}

      {/* Upload info */}
      <div className="text-sm text-gray-500">
        <p>
          {value.length} of {maxFiles} images uploaded
          {value.length > 0 && " • First image will be used as the main image"}
        </p>
        <p className="mt-1">
          Supported formats: JPEG, PNG, WebP • Max size: 4MB per image
        </p>
      </div>
    </div>
  );
}
