import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testEditFunctionality() {
  try {
    console.log('🧪 Testing edit functionality...');
    
    // Find the test product
    const testProduct = await prisma.product.findFirst({
      where: { name: 'Editable Test Product' },
      include: {
        category: true,
        images: true,
      }
    });
    
    if (!testProduct) {
      console.log('❌ Test product not found. Creating one...');
      // Run the create test product script
      return;
    }
    
    console.log('✅ Test product found:');
    console.log(`   ID: ${testProduct.id}`);
    console.log(`   Name: ${testProduct.name}`);
    console.log(`   Price: ${testProduct.price} TND`);
    console.log(`   Category: ${testProduct.category?.name || 'None'}`);
    console.log(`   Images: ${testProduct.images.length}`);
    console.log(`   Active: ${testProduct.isActive}`);
    console.log(`   Featured: ${testProduct.isFeatured}`);
    console.log('');
    
    // Test updating the product
    console.log('🔄 Testing product update...');
    
    const updatedProduct = await prisma.product.update({
      where: { id: testProduct.id },
      data: {
        name: 'Updated Test Product',
        price: 39.99,
        description: 'This product has been updated via API',
        isFeatured: false,
      },
      include: {
        category: true,
        images: true,
      }
    });
    
    console.log('✅ Product updated successfully:');
    console.log(`   New Name: ${updatedProduct.name}`);
    console.log(`   New Price: ${updatedProduct.price} TND`);
    console.log(`   New Description: ${updatedProduct.description}`);
    console.log(`   Featured: ${updatedProduct.isFeatured}`);
    console.log('');
    
    // Revert changes
    console.log('🔄 Reverting changes...');
    
    await prisma.product.update({
      where: { id: testProduct.id },
      data: {
        name: 'Editable Test Product',
        price: 49.99,
        description: 'This is a test product that you can edit to test the edit functionality',
        isFeatured: true,
      }
    });
    
    console.log('✅ Changes reverted successfully!');
    console.log('');
    console.log('🎯 Edit functionality is working correctly!');
    console.log('');
    console.log('🔗 Test URLs:');
    console.log(`   Products List: http://localhost:3000/admin/products`);
    console.log(`   Edit Product: http://localhost:3000/admin/products/${testProduct.id}/edit`);
    console.log(`   View Product: http://localhost:3000/products/${testProduct.slug}`);
    
  } catch (error) {
    console.error('❌ Error testing edit functionality:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testEditFunctionality();
