import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';

export async function GET() {
  try {
    // Check if we're missing database
    if (!process.env.DATABASE_URL) {
      return NextResponse.json(
        { message: 'Database not configured' },
        { status: 503 }
      );
    }

    // Dynamic import to avoid build-time issues
    const { getPrismaClient } = await import('@/lib/prisma');
    const prisma = getPrismaClient();

    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        id: true,
        name: true,
        email: true,
        shopifyCustomerId: true,
        emailVerified: true,
        image: true,
        role: true,
        phone: true,
        address: true,
        city: true,
        postalCode: true,
        country: true,
      }
    });

    if (!user) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { user },
      { status: 200 }
    );
  } catch (error) {
    console.error('Profile fetch error:', error);
    return NextResponse.json(
      { message: 'Something went wrong' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    console.log('🔄 Profile update request received');

    // Check if we're in build time or missing database
    if (!process.env.DATABASE_URL || process.env.SKIP_ENV_VALIDATION === '1') {
      console.log('❌ Database not available');
      return NextResponse.json(
        { message: 'Profile update not available during build' },
        { status: 503 }
      );
    }

    console.log('✅ Database URL available');

    // Dynamic import to avoid build-time issues
    let prisma;
    try {
      const { getPrismaClient } = await import('@/lib/prisma');
      prisma = getPrismaClient();
      console.log('✅ Prisma client loaded');
    } catch (importError) {
      console.error('❌ Failed to load Prisma client:', importError);
      return NextResponse.json(
        { message: 'Database connection failed' },
        { status: 500 }
      );
    }

    const session = await auth();
    console.log('🔍 Session check:', {
      hasSession: !!session,
      hasUser: !!session?.user,
      userId: session?.user?.id || 'none',
      userEmail: session?.user?.email || 'none'
    });

    if (!session || !session.user) {
      console.log('❌ No valid session found');
      return NextResponse.json(
        { message: 'Unauthorized - Please sign in again' },
        { status: 401 }
      );
    }

    if (!session.user.id) {
      console.log('❌ No user ID in session');
      return NextResponse.json(
        { message: 'Invalid session - Missing user ID' },
        { status: 401 }
      );
    }

    const { name, phone, address, city, postalCode, country } = await request.json();
    console.log('📝 Update data received:', { name, phone, address, city, postalCode, country });

    if (!name) {
      console.log('❌ Name is required but not provided');
      return NextResponse.json(
        { message: 'Name is required' },
        { status: 400 }
      );
    }

    console.log('🔄 Attempting to update user:', session.user.id);

    let updatedUser;
    try {
      updatedUser = await prisma.user.update({
        where: {
          id: session.user.id,
        },
        data: {
          name,
          phone,
          address,
          city,
          postalCode,
          country: country || 'TN',
        },
      });
      console.log('✅ User updated successfully');
    } catch (dbError) {
      console.error('❌ Database update failed:', dbError);
      return NextResponse.json(
        { message: `Database update failed: ${dbError instanceof Error ? dbError.message : 'Unknown error'}` },
        { status: 500 }
      );
    }

    // Remove password from response
    const { password, ...userWithoutPassword } = updatedUser;

    return NextResponse.json(
      { message: 'Profile updated successfully', user: userWithoutPassword },
      { status: 200 }
    );
  } catch (error) {
    console.error('Profile update error:', error);
    return NextResponse.json(
      { message: 'Something went wrong' },
      { status: 500 }
    );
  }
}
