// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Auth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(USER)

  // Profile information
  phone         String?
  address       String?
  city          String?
  postalCode    String?
  country       String?   @default("TN")

  // Relations
  accounts      Account[]
  sessions      Session[]
  orders        Order[]
  quotes        Quote[]
  bulkQuotes    BulkQuote[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime? @updatedAt
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// E-commerce Models
model Category {
  id          String    @id @default(cuid())
  name        String
  slug        String    @unique
  description String?   @db.Text
  image       String?
  parentId    String?
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  products    Product[]
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("categories")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?  @db.Text
  shortDescription String?
  sku         String?  @unique
  price       Float
  comparePrice Float?
  costPrice   Float?
  trackQuantity Boolean @default(true)
  quantity    Int      @default(0)
  lowStockThreshold Int @default(5)
  weight      Float?
  dimensions  String?  // JSON string for length, width, height

  // SEO
  metaTitle   String?
  metaDescription String?

  // Status
  isActive    Boolean  @default(true)
  isFeatured  Boolean  @default(false)

  // Relations
  categoryId  String?
  category    Category? @relation(fields: [categoryId], references: [id])
  images      ProductImage[]
  variants    ProductVariant[]
  orderItems  OrderItem[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("products")
}

model ProductImage {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  url       String
  altText   String?
  sortOrder Int     @default(0)

  createdAt DateTime @default(now())

  @@map("product_images")
}

model ProductVariant {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  name      String
  sku       String? @unique
  price     Float?
  quantity  Int     @default(0)
  isActive  Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("product_variants")
}

// Custom Models
enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

model Order {
  id                String      @id @default(cuid())
  userId            String
  user              User        @relation(fields: [userId], references: [id])
  orderNumber       String      @unique
  status            OrderStatus @default(PENDING)
  subtotal          Float
  shippingCost      Float       @default(0)
  taxAmount         Float       @default(0)
  total             Float
  currency          String      @default("TND")
  paymentMethod     String?
  paymentStatus     String      @default("pending")
  bankReceiptPath   String?
  items             OrderItem[]
  notes             String?

  // Shipping information
  shippingAddress   String?
  shippingCity      String?
  shippingPostalCode String?
  shippingCountry   String?

  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
}

model OrderItem {
  id        String   @id @default(cuid())
  orderId   String
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId String
  product   Product  @relation(fields: [productId], references: [id])
  title     String
  price     Float
  quantity  Int
  image     String?
}

model Quote {
  id              String   @id @default(cuid())
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  productId       String
  quantity        Int
  basePrice       Float
  discount        Float    @default(0)
  finalPrice      Float
  paymentMethod   String
  bankReceiptPath String?
  status          String   @default("pending")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("quotes")
}

model BulkQuote {
  id              String   @id @default(cuid())
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  items           String   @db.Text // JSON string of selected items
  totalQuantity   Int
  subtotal        Float
  discount        Float    @default(0)
  total           Float
  paymentMethod   String
  bankReceiptPath String?
  status          String   @default("pending")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("bulk_quotes")
}
