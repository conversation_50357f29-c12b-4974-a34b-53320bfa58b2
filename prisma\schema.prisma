// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Auth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          String    @default("user")
  shopifyCustomerId String?

  // Profile information
  phone         String?
  address       String?
  city          String?
  postalCode    String?
  country       String?   @default("TN")

  // Relations
  accounts      Account[]
  sessions      Session[]
  orders        Order[]
  quotes        Quote[]
  bulkQuotes    BulkQuote[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime? @updatedAt
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Custom Models
model Order {
  id                String      @id @default(cuid())
  userId            String
  user              User        @relation(fields: [userId], references: [id])
  orderNumber       String      @unique
  shopifyOrderId    String?
  status            String
  total             Float
  currency          String      @default("TND")
  paymentMethod     String?
  bankReceiptPath   String?
  items             OrderItem[]

  // Shipping information
  shippingAddress   String?
  shippingCity      String?
  shippingPostalCode String?
  shippingCountry   String?

  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
}

model OrderItem {
  id        String   @id @default(cuid())
  orderId   String
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId String
  title     String
  price     Float
  quantity  Int
  image     String?
}

model Quote {
  id              String   @id @default(cuid())
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  productId       String
  quantity        Int
  basePrice       Float
  discount        Float    @default(0)
  finalPrice      Float
  paymentMethod   String
  bankReceiptPath String?
  status          String   @default("pending")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("quotes")
}

model BulkQuote {
  id              String   @id @default(cuid())
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  items           String   @db.Text // JSON string of selected items
  totalQuantity   Int
  subtotal        Float
  discount        Float    @default(0)
  total           Float
  paymentMethod   String
  bankReceiptPath String?
  status          String   @default("pending")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("bulk_quotes")
}
