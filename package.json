{"name": "packedin-ecommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force && npm run db:seed"}, "dependencies": {"@auth/core": "^0.39.1", "@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@types/bcrypt": "^5.0.2", "@types/multer": "^1.4.12", "@types/uuid": "^10.0.0", "@uploadthing/react": "^7.3.1", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.12.1", "glob": "^10.4.5", "lucide-react": "^0.510.0", "multer": "^1.4.5-lts.1", "next": "^14.2.18", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "prisma": "^6.8.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "sharp": "^0.33.5", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.7.2", "uuid": "^11.0.3", "zod": "^3.25.36"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^8", "eslint-config-next": "14.2.18", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5"}}