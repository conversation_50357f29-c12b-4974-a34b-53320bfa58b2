import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function setupTestData() {
  try {
    console.log('🚀 Setting up test data for port 3000...');
    
    // 1. Create admin user
    console.log('👤 Creating admin user...');
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'ADMIN',
        emailVerified: new Date(),
      }
    });
    console.log(`✅ Admin user created: ${adminUser.email}`);
    
    // 2. Create test category
    console.log('📁 Creating test category...');
    const category = await prisma.category.upsert({
      where: { slug: 'test-packaging' },
      update: {},
      create: {
        name: 'Test Packaging',
        slug: 'test-packaging',
        description: 'Test packaging products for demonstration',
        isActive: true,
        sortOrder: 1,
      }
    });
    console.log(`✅ Category created: ${category.name}`);
    
    // 3. Create test products
    console.log('📦 Creating test products...');
    
    const products = [
      {
        name: 'Kraft Paper Bag',
        description: 'Eco-friendly kraft paper bag perfect for retail packaging',
        shortDescription: 'Eco-friendly kraft paper bag',
        price: 15.99,
        comparePrice: 19.99,
        sku: 'KPB-001',
        quantity: 100,
        isActive: true,
        isFeatured: true,
        categoryId: category.id,
        slug: 'kraft-paper-bag',
        images: [
          'https://via.placeholder.com/400x400/8B4513/ffffff?text=Kraft+Bag'
        ]
      },
      {
        name: 'Transparent Pouch',
        description: 'Clear transparent pouch with zip closure for food packaging',
        shortDescription: 'Clear transparent zip pouch',
        price: 8.50,
        sku: 'TP-002',
        quantity: 200,
        isActive: true,
        isFeatured: false,
        categoryId: category.id,
        slug: 'transparent-pouch',
        images: [
          'https://via.placeholder.com/400x400/87CEEB/ffffff?text=Clear+Pouch'
        ]
      },
      {
        name: 'Aluminum Packaging',
        description: 'Premium aluminum packaging for high-end products',
        shortDescription: 'Premium aluminum packaging',
        price: 25.00,
        comparePrice: 30.00,
        sku: 'AP-003',
        quantity: 50,
        isActive: true,
        isFeatured: true,
        categoryId: category.id,
        slug: 'aluminum-packaging',
        images: [
          'https://via.placeholder.com/400x400/C0C0C0/000000?text=Aluminum+Pack'
        ]
      }
    ];
    
    for (const productData of products) {
      const { images, ...productInfo } = productData;
      
      const product = await prisma.product.create({
        data: {
          ...productInfo,
          images: {
            create: images.map((url, index) => ({
              url,
              altText: `${productData.name} - Image ${index + 1}`,
              sortOrder: index,
            }))
          }
        },
        include: {
          category: true,
          images: true,
        }
      });
      
      console.log(`✅ Created product: ${product.name} (${product.id})`);
    }
    
    // 4. Test API connection
    console.log('🌐 Testing API connection...');
    try {
      const response = await fetch('http://localhost:3000/api/products');
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ API working: ${data.products?.length || 0} products returned`);
      } else {
        console.log(`⚠️ API returned status: ${response.status}`);
      }
    } catch (apiError) {
      console.log(`⚠️ API test failed: ${apiError}`);
    }
    
    console.log('\n🎉 Test data setup complete!');
    console.log('\n🔗 Test URLs:');
    console.log('   Products Page: http://localhost:3000/products');
    console.log('   Admin Dashboard: http://localhost:3000/admin');
    console.log('   API Endpoint: http://localhost:3000/api/products');
    
    console.log('\n🔑 Admin Login:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    
  } catch (error) {
    console.error('❌ Error setting up test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setupTestData();
