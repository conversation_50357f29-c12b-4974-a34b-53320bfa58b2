import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestProduct() {
  try {
    console.log('🧪 Creating test product for editing...');
    
    // Get a category to use
    const category = await prisma.category.findFirst();
    console.log(`📁 Using category: ${category?.name || 'None'}`);
    
    // Create a test product
    const testProduct = await prisma.product.create({
      data: {
        name: 'Editable Test Product',
        description: 'This is a test product that you can edit to test the edit functionality',
        shortDescription: 'Test product for editing',
        price: 49.99,
        comparePrice: 59.99,
        sku: 'EDIT-TEST-001',
        quantity: 50,
        lowStockThreshold: 5,
        weight: 0.3,
        isActive: true,
        isFeatured: true,
        trackQuantity: true,
        categoryId: category?.id || null,
        slug: 'editable-test-product',
        images: {
          create: [
            {
              url: 'https://via.placeholder.com/400x400/10b981/ffffff?text=Edit+Test+Product',
              altText: 'Editable Test Product - Main Image',
              sortOrder: 0,
            },
            {
              url: 'https://via.placeholder.com/400x400/3b82f6/ffffff?text=Edit+Test+2',
              altText: 'Editable Test Product - Secondary Image',
              sortOrder: 1,
            },
            {
              url: 'https://via.placeholder.com/400x400/f59e0b/ffffff?text=Edit+Test+3',
              altText: 'Editable Test Product - Third Image',
              sortOrder: 2,
            }
          ]
        }
      },
      include: {
        category: true,
        images: true,
      }
    });
    
    console.log('✅ Test product created successfully!');
    console.log(`   ID: ${testProduct.id}`);
    console.log(`   Name: ${testProduct.name}`);
    console.log(`   Price: ${testProduct.price} TND`);
    console.log(`   Category: ${testProduct.category?.name || 'None'}`);
    console.log(`   Images: ${testProduct.images.length} uploaded`);
    console.log(`   Slug: ${testProduct.slug}`);
    console.log('');
    console.log('🔗 Edit URL:');
    console.log(`   http://localhost:3000/admin/products/${testProduct.id}/edit`);
    console.log('');
    console.log('🎯 You can now test the edit functionality!');
    
  } catch (error) {
    console.error('❌ Error creating test product:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestProduct();
