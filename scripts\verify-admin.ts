import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyAdmin() {
  try {
    console.log('🔍 Checking admin user...');
    
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true
      }
    });
    
    if (adminUser) {
      console.log('✅ Admin user found:');
      console.log(`   Email: ${adminUser.email}`);
      console.log(`   Name: ${adminUser.name}`);
      console.log(`   Role: ${adminUser.role}`);
      console.log(`   Created: ${adminUser.createdAt}`);
      console.log('');
      console.log('🔑 Login Credentials:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: admin123');
      console.log('');
      console.log('🌐 Admin Dashboard: http://localhost:3000/admin');
      console.log('🔐 Sign In: http://localhost:3000/auth/signin');
    } else {
      console.log('❌ Admin user not found!');
    }
    
    // Check total users
    const userCount = await prisma.user.count();
    console.log(`👥 Total users in database: ${userCount}`);
    
    // Check products
    const productCount = await prisma.product.count();
    console.log(`📦 Total products in database: ${productCount}`);
    
    // Check categories
    const categoryCount = await prisma.category.count();
    console.log(`📁 Total categories in database: ${categoryCount}`);
    
  } catch (error) {
    console.error('❌ Error checking admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyAdmin();
