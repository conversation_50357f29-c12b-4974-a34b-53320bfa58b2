import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testProductsAPI() {
  try {
    console.log('🧪 Testing Products API and Database...');
    
    // 1. Check how many products exist in database
    const totalProducts = await prisma.product.count();
    console.log(`📊 Total products in database: ${totalProducts}`);
    
    // 2. Check active products
    const activeProducts = await prisma.product.count({
      where: { isActive: true }
    });
    console.log(`✅ Active products: ${activeProducts}`);
    
    // 3. Get some sample products with details
    const sampleProducts = await prisma.product.findMany({
      take: 5,
      include: {
        category: true,
        images: true,
        variants: true,
      },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log('\n📦 Sample Products:');
    sampleProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   - ID: ${product.id}`);
      console.log(`   - Price: ${product.price} TND`);
      console.log(`   - Active: ${product.isActive}`);
      console.log(`   - Category: ${product.category?.name || 'None'}`);
      console.log(`   - Images: ${product.images.length}`);
      console.log(`   - Slug: ${product.slug}`);
      console.log('');
    });
    
    // 4. Test API endpoint
    console.log('🌐 Testing API endpoint...');
    try {
      const response = await fetch('http://localhost:3001/api/products');
      const data = await response.json();
      
      if (response.ok) {
        console.log(`✅ API Response successful`);
        console.log(`📊 API returned ${data.products?.length || 0} products`);
        console.log(`📄 Pagination: Page ${data.pagination?.page || 1} of ${data.pagination?.pages || 1}`);
        
        if (data.products && data.products.length > 0) {
          console.log('\n🔍 First API product:');
          const firstProduct = data.products[0];
          console.log(`   - Name: ${firstProduct.name}`);
          console.log(`   - Price: ${firstProduct.price} TND`);
          console.log(`   - Images: ${firstProduct.images?.length || 0}`);
          console.log(`   - Category: ${firstProduct.category?.name || 'None'}`);
        }
      } else {
        console.log(`❌ API Error: ${response.status} - ${data.error || 'Unknown error'}`);
      }
    } catch (apiError) {
      console.log(`❌ API Request failed: ${apiError}`);
    }
    
    // 5. Create a test product if none exist
    if (totalProducts === 0) {
      console.log('\n🔧 No products found. Creating test products...');
      
      // Create a test category first
      const testCategory = await prisma.category.upsert({
        where: { slug: 'test-category' },
        update: {},
        create: {
          name: 'Test Category',
          slug: 'test-category',
          description: 'A test category for products',
          isActive: true,
          sortOrder: 1,
        }
      });
      
      // Create test products
      const testProducts = [
        {
          name: 'Test Product 1',
          description: 'This is a test product for the products page',
          price: 29.99,
          slug: 'test-product-1',
          isActive: true,
          isFeatured: true,
          quantity: 100,
          categoryId: testCategory.id,
        },
        {
          name: 'Test Product 2',
          description: 'Another test product with different features',
          price: 49.99,
          comparePrice: 59.99,
          slug: 'test-product-2',
          isActive: true,
          isFeatured: false,
          quantity: 50,
          categoryId: testCategory.id,
        },
        {
          name: 'Test Product 3',
          description: 'A third test product to populate the catalog',
          price: 19.99,
          slug: 'test-product-3',
          isActive: true,
          isFeatured: true,
          quantity: 75,
          categoryId: testCategory.id,
        }
      ];
      
      for (const productData of testProducts) {
        const product = await prisma.product.create({
          data: {
            ...productData,
            images: {
              create: [
                {
                  url: `https://via.placeholder.com/400x400/10b981/ffffff?text=${encodeURIComponent(productData.name)}`,
                  altText: `${productData.name} - Main Image`,
                  sortOrder: 0,
                }
              ]
            }
          },
          include: {
            category: true,
            images: true,
          }
        });
        
        console.log(`✅ Created: ${product.name} (${product.id})`);
      }
      
      console.log('\n🎉 Test products created successfully!');
    }
    
    console.log('\n🔗 Test URLs:');
    console.log(`   Products Page: http://localhost:3001/products`);
    console.log(`   API Endpoint: http://localhost:3001/api/products`);
    console.log(`   Admin Products: http://localhost:3001/admin/products`);
    
    console.log('\n✅ Products API test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing products API:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testProductsAPI();
